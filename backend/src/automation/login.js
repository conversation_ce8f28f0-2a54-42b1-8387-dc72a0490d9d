const { chromium } = require('playwright');
const path = require('path');
const { sleep } = require('../utils');
const AntidetectManager = require('../antidetect/antidetect-manager');
const HumanBehavior = require('../antidetect/human-behavior');
const ProxyService = require('../services/ProxyService');

class TikTokLoginAutomation {
  constructor(wsServer, dbManager) {
    this.wsServer = wsServer;
    this.dbManager = dbManager;
    this.browsers = new Map(); // accountId -> browser instance
    this.antidetectManager = new AntidetectManager();
    this.proxyService = new ProxyService();
    this.loginChecker = null; // Background checker interval

    // Bắt đầu background checker
    this.startBackgroundLoginChecker();
  }

  /**
   * Đăng nhập tài khoản TikTok
   */
  async loginAccount(accountId) {
    try {
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      // Kiểm tra và đóng browser cũ nếu có
      await this.closeBrowser(accountId);

      this.wsServer.sendLog('info', `Starting login for ${account.username}`, accountId);

      // Tạo profile directory cho account
      const profileDir = path.join(__dirname, '../../profiles', accountId);

      // Lấy hoặc tạo persona cho account
      let persona = account.persona;
      if (!persona) {
        // Chọn persona dựa trên proxy location nếu có
        const preferredRegion = account.proxy ?
          this.antidetectManager.mapCountryToRegion(account.proxy.country) : null;
        persona = await this.antidetectManager.selectRandomPersona(preferredRegion);

        // Lưu persona vào account
        await this.dbManager.updateAccount(accountId, { persona: persona });
        this.wsServer.sendLog('info', `Assigned new persona ${persona.id} (${persona.platform}) to account ${account.username}`, accountId);
      } else {
        // Tái sử dụng persona đã có
        this.wsServer.sendLog('info', `Reusing existing persona ${persona.id} (${persona.platform}) for account ${account.username}`, accountId);

        // Kiểm tra xem persona có tồn tại trong ngân hàng không (có thể đã bị xóa)
        const existingPersona = await this.antidetectManager.getPersonaById(persona.id);
        if (!existingPersona) {
          this.wsServer.sendLog('warning', `Persona ${persona.id} not found in bank, selecting new one`, accountId);
          const preferredRegion = account.proxy ?
            this.antidetectManager.mapCountryToRegion(account.proxy.country) : null;
          persona = await this.antidetectManager.selectRandomPersona(preferredRegion);
          await this.dbManager.updateAccount(accountId, { persona: persona });
          this.wsServer.sendLog('info', `Assigned replacement persona ${persona.id} to account ${account.username}`, accountId);
        }
      }

      // Tạo context options với antidetect (sử dụng Google-specific nếu cần)
      const contextOptions = account.useGoogleLogin ?
        await this.antidetectManager.createGoogleContextOptions(persona, account.proxy, accountId) :
        await this.antidetectManager.createContextOptions(persona, account.proxy, accountId);
      const launchOptions = this.antidetectManager.createBrowserLaunchOptions();

      // Log proxy information for debugging
      if (account.proxy && account.proxy.type !== 'No proxy') {
        this.wsServer.sendLog('info', `Using proxy: ${account.proxy.type} ${account.proxy.host}:${account.proxy.port}`, accountId);
        if (contextOptions.proxy) {
          this.wsServer.sendLog('info', `Proxy config: ${contextOptions.proxy.server}`, accountId);
        } else {
          this.wsServer.sendLog('warning', `Proxy not configured in context options (may be SOCKS5 with auth)`, accountId);
        }
      } else {
        this.wsServer.sendLog('info', `No proxy configured - using direct connection`, accountId);
      }

      // Merge options với proxy có ưu tiên cao nhất
      const browserOptions = {
        ...launchOptions,
        ...contextOptions,
        headless: false // Hiển thị browser để user có thể giải CAPTCHA
      };

      // Khởi tạo browser với profile và antidetect
      const browser = await chromium.launchPersistentContext(profileDir, browserOptions);

      this.browsers.set(accountId, browser);

      // Inject spoofing script
      const spoofingScript = this.antidetectManager.createSpoofingScript(persona);
      await browser.addInitScript(spoofingScript);

      const page = await browser.newPage();

      // Initialize human behavior simulation
      const humanBehavior = new HumanBehavior(page);

      // Kiểm tra IP thực tế để verify proxy
      await this.verifyProxyConnection(page, accountId, account.proxy);

      // Load cookies trước khi điều hướng
      await this.loadCookies(page, accountId);

      // Điều hướng đến trang đăng nhập TikTok với retry logic
      this.wsServer.sendLog('info', `Navigating to TikTok login page`, accountId);

      const targetUrl = account.useGoogleLogin
        ? 'https://www.tiktok.com/login'
        : 'https://www.tiktok.com/login/phone-or-email/email';

      // Retry logic cho page navigation
      let navigationSuccess = false;
      let retryCount = 0;
      const maxRetries = 3;

      while (!navigationSuccess && retryCount < maxRetries) {
        try {
          retryCount++;
          this.wsServer.sendLog('info', `Navigation attempt ${retryCount}/${maxRetries}`, accountId);

          await page.goto(targetUrl, {
            waitUntil: 'domcontentloaded', // Thay đổi từ 'networkidle' thành 'domcontentloaded'
            timeout: 60000 // Tăng timeout lên 60 giây
          });

          navigationSuccess = true;
          this.wsServer.sendLog('success', `Successfully navigated to TikTok login page`, accountId);
        } catch (error) {
          this.wsServer.sendLog('warning', `Navigation attempt ${retryCount} failed: ${error.message}`, accountId);

          if (retryCount >= maxRetries) {
            throw new Error(`Failed to navigate to TikTok after ${maxRetries} attempts: ${error.message}`);
          }

          // Chờ trước khi retry
          await sleep(3000);
        }
      }

      // Simulate human behavior - explore page briefly
      await humanBehavior.explorePage(2000);
      await humanBehavior.randomDelay(1000, 1000);

      // Kiểm tra xem đã đăng nhập chưa (sau khi load cookies)
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        this.wsServer.sendLog('success', `Already logged in for account ${accountId}`, accountId);

        // Lưu cookies
        await this.saveCookies(page, accountId);

        // Không đóng browser, để user tự xác nhận hoàn tất
        await this.dbManager.updateAccount(accountId, {
          status: 'login_completed',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'login_completed');
        this.wsServer.sendLog('info', `Login completed. Please click "Đăng nhập hoàn tất" to finish.`, accountId);
        return true;
      }

      // Chỉ cho phép đăng nhập thủ công
      this.wsServer.sendLog('info', `⚠️  MANUAL LOGIN REQUIRED: Please complete the login process manually in the browser.`, accountId);
      this.wsServer.sendLog('info', `Browser will remain open for manual login. Click "Đăng nhập hoàn tất" when done.`, accountId);

      // Chờ đăng nhập thành công (timeout dài cho manual process)
      const loginSuccess = await this.waitForLoginSuccess(page, accountId, 600000); // 10 phút

      if (loginSuccess) {
        this.wsServer.sendLog('success', `Manual login successful for account ${accountId}`, accountId);

        // Lưu cookies sau khi đăng nhập thành công
        await this.saveCookies(page, accountId);

        // Không đóng browser, để user tự xác nhận hoàn tất
        await this.dbManager.updateAccount(accountId, {
          status: 'login_completed',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'login_completed');
        this.wsServer.sendLog('info', `Login completed. Please click "Đăng nhập hoàn tất" to finish.`, accountId);
        return true;
      } else {
        throw new Error('Manual login timeout - please try again');
      }

      // COMMENTED OUT: Automatic login logic
      // if (account.useGoogleLogin) {
      //   // Google OAuth login
      //   return await this.handleGoogleLogin(page, accountId, humanBehavior);
      // } else {
      //   // Regular username/password login
      //   return await this.handleRegularLogin(page, account, accountId, humanBehavior);
      // }

    } catch (error) {
      this.wsServer.sendLog('error', `Login failed for account ${accountId}: ${error.message}`, accountId);

      // Kiểm tra xem có phải lỗi timeout hay navigation không
      const isNavigationError = error.message.includes('Timeout') ||
                                error.message.includes('Navigation') ||
                                error.message.includes('goto');

      if (isNavigationError) {
        // Với lỗi navigation, không đóng browser ngay, để user có thể thử lại
        this.wsServer.sendLog('warning', `Navigation error - browser kept open for manual retry`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'error' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'error');
        return false;
      } else {
        // Với các lỗi khác, đóng browser
        await this.dbManager.updateAccount(accountId, { status: 'error' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'error');
        await this.closeBrowser(accountId);
        return false;
      }
    }
  }

  /**
   * Handle regular username/password login
   * COMMENTED OUT: Only manual login is allowed
   */
  /*
  async handleRegularLogin(page, account, accountId, humanBehavior) {
    try {
      // Điền thông tin đăng nhập
      this.wsServer.sendLog('info', `Filling login credentials`, accountId);

      // Use human-like form filling
      await humanBehavior.fillFormHumanLike([
        { selector: 'input[name="username"]', value: account.username },
        { selector: 'input[type="password"]', value: account.password }
      ]);

      // Simulate hesitation before clicking login
      await humanBehavior.simulateHesitation('submit');

      // Click nút đăng nhập with human behavior
      await humanBehavior.humanClick('button[data-e2e="login-button"]');

      this.wsServer.sendLog('info', `Login form submitted, waiting for response...`, accountId);
      this.wsServer.sendLog('info', `If you need to complete any additional steps (2FA, CAPTCHA), please do so in the browser`, accountId);
      await sleep(3000, 5000);

      // Kiểm tra CAPTCHA hoặc lỗi đăng nhập - nếu có CAPTCHA, user sẽ giải thủ công
      const needsCaptcha = await this.checkForCaptcha(page);
      if (needsCaptcha) {
        this.wsServer.sendLog('warning', `CAPTCHA detected for ${account.username}. Please solve manually in the browser.`, accountId);
        // Không thay đổi status, để user giải CAPTCHA thủ công
      }

      // Kiểm tra đăng nhập thành công (tăng timeout cho manual input)
      const loginSuccess = await this.waitForLoginSuccess(page, accountId, 120000); // 2 phút
      if (loginSuccess) {
        this.wsServer.sendLog('success', `Login successful for ${account.username}`, accountId);

        // Lưu cookies sau khi đăng nhập thành công
        await this.saveCookies(page, accountId);

        // Không đóng browser, để user tự xác nhận hoàn tất
        await this.dbManager.updateAccount(accountId, {
          status: 'login_completed',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'login_completed');
        this.wsServer.sendLog('info', `Login completed. Please click "Đăng nhập hoàn tất" to finish.`, accountId);
        return true;
      } else {
        throw new Error('Login failed - unknown error');
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Regular login failed for account ${accountId}: ${error.message}`, accountId);
      throw error;
    }
  }
  */

  /**
   * Handle Google OAuth login - Manual process
   * COMMENTED OUT: Only manual login is allowed
   */
  /*
  async handleGoogleLogin(page, accountId, humanBehavior) {
    try {
      this.wsServer.sendLog('info', `Starting manual Google login process`, accountId);

      // Chỉ cần mở browser đến trang login, user sẽ tự làm tất cả
      this.wsServer.sendLog('info', `Browser opened for manual login. Please complete the entire login process manually.`, accountId);
      this.wsServer.sendLog('info', `Steps to follow:`, accountId);
      this.wsServer.sendLog('info', `1) Click on "Continue with Google" or similar button`, accountId);
      this.wsServer.sendLog('info', `2) Enter your Google email/phone`, accountId);
      this.wsServer.sendLog('info', `3) Enter your Google password`, accountId);
      this.wsServer.sendLog('info', `4) Complete any 2FA/verification if required`, accountId);
      this.wsServer.sendLog('info', `5) Wait for redirect back to TikTok`, accountId);
      this.wsServer.sendLog('info', `⚠️  IMPORTANT: Please complete the login process manually. Browser will close automatically when login is successful.`, accountId);

      // Đặt trạng thái là đang đăng nhập
      await this.dbManager.updateAccount(accountId, { status: 'logging_in' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'logging_in');

      // Chờ đăng nhập thành công (timeout dài cho manual process)
      const loginSuccess = await this.waitForLoginSuccess(page, accountId, 600000); // 10 phút

      if (loginSuccess) {
        this.wsServer.sendLog('success', `Manual Google login successful for account ${accountId}`, accountId);

        // Lưu cookies sau khi đăng nhập thành công
        await this.saveCookies(page, accountId);

        // Không đóng browser, để user tự xác nhận hoàn tất
        await this.dbManager.updateAccount(accountId, {
          status: 'login_completed',
          stats: {
            followsToday: 0,
            followsThisSession: 0,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'login_completed');
        this.wsServer.sendLog('info', `Login completed. Please click "Đăng nhập hoàn tất" to finish.`, accountId);
        return true;
      } else {
        throw new Error('Manual login timeout - please try again');
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Manual Google login failed for account ${accountId}: ${error.message}`, accountId);
      throw error;
    }
  }
  */

  /**
   * Kiểm tra xem đã đăng nhập chưa
   */
  async checkIfLoggedIn(page) {
    try {
      // Kiểm tra các element chỉ xuất hiện khi đã đăng nhập
      const profileButton = page.locator('[data-e2e="profile-icon"]');
      const uploadButton = page.locator('[data-e2e="upload-icon"]');

      const isLoggedIn = await Promise.race([
        profileButton.isVisible().then(visible => visible),
        uploadButton.isVisible().then(visible => visible),
        sleep(5000).then(() => false)
      ]);

      return isLoggedIn;
    } catch (error) {
      return false;
    }
  }

  /**
   * Kiểm tra CAPTCHA
   */
  async checkForCaptcha(page) {
    try {
      const captchaSelectors = [
        '.captcha_verify_container',
        '[data-testid="captcha"]',
        '.verify-slider-track',
        '.secsdk-captcha-wrapper'
      ];

      for (const selector of captchaSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Chờ user giải CAPTCHA
   */
  async waitForCaptchaSolution(page, accountId, timeout = 300000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const hasCaptcha = await this.checkForCaptcha(page);
      if (!hasCaptcha) {
        this.wsServer.sendLog('success', `CAPTCHA solved for account`, accountId);
        return true;
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Chờ đăng nhập thành công
   */
  async waitForLoginSuccess(page, accountId, timeout = 30000) {
    const startTime = Date.now();
    let lastLogTime = 0;
    const logInterval = 30000; // Log mỗi 30 giây

    this.wsServer.sendLog('info', `Waiting for login completion (timeout: ${Math.round(timeout/1000)}s)...`, accountId);

    while (Date.now() - startTime < timeout) {
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        return true;
      }

      // Log tiến trình mỗi 30 giây
      const elapsed = Date.now() - startTime;
      if (elapsed - lastLogTime > logInterval) {
        const remaining = Math.round((timeout - elapsed) / 1000);
        this.wsServer.sendLog('info', `Still waiting for login... (${remaining}s remaining)`, accountId);
        lastLogTime = elapsed;
      }

      // Kiểm tra lỗi đăng nhập (chỉ sau 30 giây để tránh false positive)
      if (elapsed > 30000) {
        const hasError = await this.checkForLoginError(page);
        if (hasError) {
          this.wsServer.sendLog('warning', `Login error detected, but continuing to wait...`, accountId);
          // Không throw error ngay, chờ thêm một chút
          await sleep(10000);

          // Kiểm tra lại sau khi chờ
          const stillHasError = await this.checkForLoginError(page);
          const stillNotLoggedIn = !(await this.checkIfLoggedIn(page));

          // Chỉ throw error nếu đã gần hết timeout và vẫn có lỗi
          if (stillHasError && stillNotLoggedIn && (timeout - elapsed) < 30000) {
            this.wsServer.sendLog('warning', `Persistent login error detected near timeout`, accountId);
            // Không throw error, để timeout tự nhiên
          }
        }
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Kiểm tra lỗi đăng nhập
   */
  async checkForLoginError(page) {
    try {
      const errorSelectors = [
        '.TUXTextError',
        '[data-e2e="login-error"]',
        '.error-message',
        '.login-error',
        '[class*="error"]'
      ];

      for (const selector of errorSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible({ timeout: 1000 })) {
          // Kiểm tra nội dung error để đảm bảo đây là lỗi thật
          const errorText = await element.textContent();
          if (errorText && errorText.trim().length > 0) {
            // Chỉ coi là lỗi nếu có text cụ thể về lỗi đăng nhập
            const errorKeywords = ['incorrect', 'invalid', 'wrong', 'failed', 'error', 'sai', 'lỗi'];
            const hasErrorKeyword = errorKeywords.some(keyword =>
              errorText.toLowerCase().includes(keyword)
            );
            if (hasErrorKeyword) {
              return true;
            }
          }
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Lưu cookies sau khi đăng nhập thành công
   */
  async saveCookies(page, accountId) {
    try {
      const cookies = await page.context().cookies();
      const cookiesPath = path.join(__dirname, '../../profiles', accountId, 'cookies.json');

      // Tạo thư mục nếu chưa có
      const fs = require('fs').promises;
      await fs.mkdir(path.dirname(cookiesPath), { recursive: true });

      // Lưu cookies
      await fs.writeFile(cookiesPath, JSON.stringify(cookies, null, 2));
      this.wsServer.sendLog('info', `Cookies saved for account ${accountId}`, accountId);
    } catch (error) {
      this.wsServer.sendLog('warning', `Failed to save cookies for account ${accountId}: ${error.message}`, accountId);
    }
  }

  /**
   * Verify proxy connection by checking actual IP
   */
  async verifyProxyConnection(page, accountId, proxy) {
    try {
      if (!proxy || proxy.type === 'No proxy') {
        this.wsServer.sendLog('info', `Using direct connection (no proxy)`, accountId);
        return;
      }

      this.wsServer.sendLog('info', `Verifying proxy connection...`, accountId);

      // Try to get IP from a simple service
      try {
        await page.goto('https://httpbin.org/ip', { timeout: 10000 });
        const content = await page.textContent('body');
        const ipData = JSON.parse(content);
        const actualIP = ipData.origin;

        this.wsServer.sendLog('info', `Current IP: ${actualIP}`, accountId);

        // Check if IP matches proxy IP (if available)
        if (proxy.ip && actualIP.includes(proxy.ip)) {
          this.wsServer.sendLog('success', `✅ Proxy working correctly - IP matches`, accountId);
        } else if (proxy.ip) {
          this.wsServer.sendLog('warning', `⚠️  Proxy IP mismatch. Expected: ${proxy.ip}, Got: ${actualIP}`, accountId);
        } else {
          this.wsServer.sendLog('info', `Proxy connection established (IP: ${actualIP})`, accountId);
        }
      } catch (error) {
        this.wsServer.sendLog('warning', `Could not verify IP (${error.message}) - continuing anyway`, accountId);
      }
    } catch (error) {
      this.wsServer.sendLog('warning', `Proxy verification failed: ${error.message}`, accountId);
    }
  }

  /**
   * Load cookies cho account
   */
  async loadCookies(page, accountId) {
    try {
      const cookiesPath = path.join(__dirname, '../../profiles', accountId, 'cookies.json');
      const fs = require('fs').promises;

      const cookiesData = await fs.readFile(cookiesPath, 'utf8');
      const cookies = JSON.parse(cookiesData);

      await page.context().addCookies(cookies);
      this.wsServer.sendLog('info', `Cookies loaded for account ${accountId}`, accountId);
      return true;
    } catch (error) {
      this.wsServer.sendLog('info', `No existing cookies found for account ${accountId}`, accountId);
      return false;
    }
  }

  /**
   * Kiểm tra và cập nhật status nếu user đã đăng nhập thành công thủ công
   */
  async checkAndUpdateLoginStatus(accountId) {
    try {
      const browser = this.browsers.get(accountId);
      if (!browser) {
        return false;
      }

      const account = await this.dbManager.getAccountById(accountId);
      if (!account || account.status !== 'error') {
        return false;
      }

      // Lấy page đầu tiên
      const pages = browser.pages();
      if (pages.length === 0) {
        return false;
      }

      const page = pages[0];
      const isLoggedIn = await this.checkIfLoggedIn(page);

      if (isLoggedIn) {
        this.wsServer.sendLog('success', `Manual login detected for account ${accountId}`, accountId);

        // Lưu cookies
        await this.saveCookies(page, accountId);

        // Cập nhật status thành login_completed
        await this.dbManager.updateAccount(accountId, {
          status: 'login_completed',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'login_completed');
        this.wsServer.sendLog('info', `Login completed. Please click "Đăng nhập hoàn tất" to finish.`, accountId);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error checking login status for ${accountId}:`, error);
      return false;
    }
  }

  /**
   * Bắt đầu background checker cho các account có lỗi
   */
  startBackgroundLoginChecker() {
    if (this.loginChecker) {
      return; // Đã chạy rồi
    }

    this.loginChecker = setInterval(async () => {
      try {
        const accounts = await this.dbManager.getAccounts();
        const errorAccounts = accounts.filter(acc => acc.status === 'error');

        for (const account of errorAccounts) {
          if (this.browsers.has(account.id)) {
            await this.checkAndUpdateLoginStatus(account.id);
          }
        }
      } catch (error) {
        console.error('Error in background login checker:', error);
      }
    }, 10000); // Kiểm tra mỗi 10 giây
  }

  /**
   * Dừng background checker
   */
  stopBackgroundLoginChecker() {
    if (this.loginChecker) {
      clearInterval(this.loginChecker);
      this.loginChecker = null;
    }
  }

  /**
   * Đóng browser cho account
   */
  async closeBrowser(accountId) {
    const browser = this.browsers.get(accountId);
    if (browser) {
      try {
        await browser.close();
      } catch (error) {
        console.error(`Error closing browser for account ${accountId}:`, error);
      } finally {
        this.browsers.delete(accountId);
      }
    }

    // Cleanup SOCKS5 bridge if exists
    try {
      await this.proxyService.stopSocks5Bridge(accountId);
    } catch (error) {
      console.error(`Error stopping SOCKS5 bridge for account ${accountId}:`, error);
    }
  }

  /**
   * Lấy browser instance cho account
   */
  getBrowser(accountId) {
    return this.browsers.get(accountId);
  }

  /**
   * Đóng tất cả browsers
   */
  async closeAllBrowsers() {
    // Dừng background checker
    this.stopBackgroundLoginChecker();

    for (const [accountId, browser] of this.browsers) {
      try {
        await browser.close();
      } catch (error) {
        console.error(`Error closing browser for ${accountId}:`, error);
      }
    }
    this.browsers.clear();

    // Cleanup all SOCKS5 bridges
    try {
      await this.proxyService.stopAllSocks5Bridges();
    } catch (error) {
      console.error(`Error stopping SOCKS5 bridges:`, error);
    }
  }
}

module.exports = TikTokLoginAutomation;
