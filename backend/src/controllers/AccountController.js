const { v4: uuidv4 } = require('uuid');

class AccountController {
  constructor(db<PERSON>ana<PERSON>, wsServer) {
    this.dbManager = dbManager;
    this.wsServer = wsServer;
  }

  /**
   * Get all accounts
   */
  async getAccounts(ws, data) {
    try {
      const accounts = await this.dbManager.getAccounts();
      this.wsServer.sendSuccess(ws, 'Accounts retrieved successfully', { accounts });
    } catch (error) {
      this.wsServer.sendError(ws, `Failed to get accounts: ${error.message}`);
    }
  }

  /**
   * Create new account
   */
  async createAccount(ws, data) {
    try {
      const { username, password, useGoogleLogin, proxy, ipChecker } = data;

      // Validation
      if (!useGoogleLogin && (!username || !password)) {
        this.wsServer.sendError(ws, 'Username and password are required for non-Google accounts');
        return;
      }

      if (!proxy || !proxy.host || !proxy.port) {
        this.wsServer.sendError(ws, 'Proxy information is required');
        return;
      }

      // Create account object
      const newAccount = {
        id: uuidv4(),
        username: username || null,
        password: password || null,
        useGoogleLogin: useGoogleLogin || false,
        status: 'not_logged_in',
        proxy: {
          type: proxy.type || 'HTTP',
          host: proxy.host,
          port: parseInt(proxy.port),
          username: proxy.username || null,
          password: proxy.password || null,
          country: proxy.country || null,
          city: proxy.city || null,
          isActive: true
        },
        ipChecker: ipChecker || 'IP2Location',
        persona: null, // Will be assigned when creating browser context
        stats: {
          followsToday: 0,
          followsThisSession: 0,
          lastActivity: null
        }
      };

      // Add to database
      await this.dbManager.addAccount(newAccount);
      
      this.wsServer.sendSuccess(ws, `Account created successfully`);
      this.wsServer.sendLog('info', `New account created: ${username || 'Google account'}`);
      
      // Send updated accounts list to all clients
      const accounts = await this.dbManager.getAccounts();
      this.wsServer.broadcast({
        type: 'success',
        message: 'Accounts updated',
        data: { accounts }
      });

    } catch (error) {
      this.wsServer.sendError(ws, `Failed to create account: ${error.message}`);
    }
  }

  /**
   * Delete account
   */
  async deleteAccount(ws, data) {
    try {
      const { accountId } = data;

      if (!accountId) {
        this.wsServer.sendError(ws, 'Account ID is required');
        return;
      }

      // Check if account exists
      const account = await this.dbManager.getAccount(accountId);
      if (!account) {
        this.wsServer.sendError(ws, 'Account not found');
        return;
      }

      // Delete account from database
      await this.dbManager.deleteAccount(accountId);
      
      this.wsServer.sendSuccess(ws, `Account deleted successfully`);
      this.wsServer.sendLog('info', `Account deleted: ${account.username || accountId}`);
      
      // Send updated accounts list to all clients
      const accounts = await this.dbManager.getAccounts();
      this.wsServer.broadcast({
        type: 'success',
        message: 'Accounts updated',
        data: { accounts }
      });

    } catch (error) {
      this.wsServer.sendError(ws, `Failed to delete account: ${error.message}`);
    }
  }

  /**
   * Login account
   */
  async loginAccount(ws, data, loginAutomation) {
    try {
      const { accountId } = data;
      if (!accountId) {
        this.wsServer.sendError(ws, 'Account ID is required');
        return;
      }

      this.wsServer.sendLog('info', `Starting login process for account ${accountId}`);
      const success = await loginAutomation.loginAccount(accountId);

      if (success) {
        this.wsServer.sendSuccess(ws, 'Login completed successfully');
      } else {
        this.wsServer.sendError(ws, 'Login failed');
      }
    } catch (error) {
      this.wsServer.sendError(ws, `Login error: ${error.message}`);
    }
  }

  /**
   * Login multiple accounts
   */
  async loginAccountsBatch(ws, data, loginAutomation) {
    try {
      const { accountIds } = data;
      if (!accountIds || !Array.isArray(accountIds)) {
        this.wsServer.sendError(ws, 'Account IDs array is required');
        return;
      }

      this.wsServer.sendLog('info', `Starting batch login for ${accountIds.length} accounts`);

      // Login accounts sequentially to avoid overwhelming the system
      for (const accountId of accountIds) {
        try {
          await loginAutomation.loginAccount(accountId);
          // Small delay between logins
          await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
          this.wsServer.sendLog('error', `Failed to login account ${accountId}: ${error.message}`);
        }
      }

      this.wsServer.sendSuccess(ws, 'Batch login completed');
    } catch (error) {
      this.wsServer.sendError(ws, `Batch login error: ${error.message}`);
    }
  }

  /**
   * Complete login manually - close browser and update status to ready
   */
  async completeLogin(ws, data, loginAutomation) {
    try {
      const { accountId } = data;
      if (!accountId) {
        this.wsServer.sendError(ws, 'Account ID is required');
        return;
      }

      // Check if account exists and is in correct state
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        this.wsServer.sendError(ws, 'Account not found');
        return;
      }

      if (account.status !== 'login_completed' && account.status !== 'need_manual_login') {
        this.wsServer.sendError(ws, 'Account is not in a state that can be completed');
        return;
      }

      this.wsServer.sendLog('info', `Completing login for account ${accountId}`, accountId);

      // Close browser if it's still open
      await loginAutomation.closeBrowser(accountId);

      // Update account status to ready
      await this.dbManager.updateAccount(accountId, {
        status: 'ready',
        stats: {
          ...account.stats,
          lastActivity: new Date().toISOString()
        }
      });

      this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
      this.wsServer.sendLog('success', `Login completed successfully for account ${accountId}`, accountId);
      this.wsServer.sendSuccess(ws, 'Login completed successfully');

    } catch (error) {
      this.wsServer.sendError(ws, `Failed to complete login: ${error.message}`);
      this.wsServer.sendLog('error', `Failed to complete login for account ${data.accountId}: ${error.message}`, data.accountId);
    }
  }
}

module.exports = AccountController;
