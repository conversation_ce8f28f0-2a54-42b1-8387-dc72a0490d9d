{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 3, "host": "lh3.googleusercontent.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "ssl.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 3, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 1, "host": "www.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "lh3.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.googleadservices.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 3, "host": "whatismyipaddress.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "fonts.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "app.fusebox.fm", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 3, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "lh3.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "lh3.googleusercontent.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 1, "host": "apis.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "signaler-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABodHRwczovL2dvb2dsZS5jb20udm4AAAA=", false, 0], "broken_count": 1, "host": "accounts.google.com.vn", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "broken_count": 1, "host": "passwordsleakcheck-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 2, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "lf16-tiktok-web.tiktokcdn-us.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "lf16-cdn-tos.tiktokcdn-us.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "**********", "host": "storage.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 5, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.ibytedtos.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-sg.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "****************0", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.pub.network", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398503226095574", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398503228721181", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://zipthelake.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://a.omappapi.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395997626194591", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cdn.onesignal.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://map.whatismyipaddress.info", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://app.fusebox.fm", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398503231418054", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://d.pub.network", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://whatismyipaddress.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3doYXRpc215aXBhZGRyZXNzLmNvbQAAAA==", false, 0], "server": "https://cmp.inmobi.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398503238802072", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogs.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://signaler-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL2dvb2dsZS5jb20udm4AAAA=", false, 0], "server": "https://accounts.google.com.vn", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://starling-va.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://login-no1a.www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-va.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sign-useast2a.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sg.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p19-sign.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p19-common-sg.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-sign.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2h0dHBiaW4ub3JnAA==", false, 0], "server": "https://httpbin.org", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb.tiktokw.us", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398505636930906", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sgali-mcs.byteoversea.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs.tiktokv.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktokw.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sign-sg.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-pu-sign-useast8.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://im-api.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://starling-ttp2.tiktokv.us", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396007230778033", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-web.tiktokcdn-us.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396007231053770", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-cdn-tos.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-sg.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://webcast.us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p19-pu-sign-useast8.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sign-va.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sign-no.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://v19-webapp-prime.us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon16-normal-useast5.tiktokv.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mssdk-ttp2.tiktokw.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website-login.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs.tiktokw.us", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://lh3.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}